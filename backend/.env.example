# Database Configuration
DATABASE_URL=postgresql+asyncpg://username:password@localhost:5432/school_management
DATABASE_URL_SYNC=postgresql://username:password@localhost:5432/school_management

# Redis Configuration (for caching and background tasks)
REDIS_URL=redis://localhost:6379/0

# JWT Configuration
SECRET_KEY=your-super-secret-key-here-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Email Configuration (SendGrid)
SENDGRID_API_KEY=your-sendgrid-api-key
FROM_EMAIL=<EMAIL>

# SMS Configuration (Twilio)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********

# File Upload Configuration
MAX_FILE_SIZE=********  # 10MB
UPLOAD_DIR=uploads/
ALLOWED_EXTENSIONS=pdf,doc,docx,jpg,jpeg,png,gif

# Application Configuration
APP_NAME=School Management System
APP_VERSION=1.0.0
DEBUG=True
ENVIRONMENT=development

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080

# Pagination
DEFAULT_PAGE_SIZE=20
MAX_PAGE_SIZE=100
