"""Update fee structure model for class selection

Revision ID: 68bbc83611b7
Revises: 
Create Date: 2025-07-11 03:41:35.936549

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '68bbc83611b7'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('fee_structures', sa.Column('applicable_to', sa.String(length=20), nullable=False))
    op.add_column('fee_structures', sa.Column('class_ids', sa.JSON(), nullable=True))
    op.drop_column('fee_structures', 'class_level')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('fee_structures', sa.Column('class_level', sa.VARCHAR(length=50), nullable=False))
    op.drop_column('fee_structures', 'class_ids')
    op.drop_column('fee_structures', 'applicable_to')
    # ### end Alembic commands ###
