"""Update user roles for platform architecture

Revision ID: 4b64de626935
Revises: 3272e46e9feb
Create Date: 2025-09-16 19:03:00.264119

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '4b64de626935'
down_revision = '3272e46e9feb'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('users', 'school_id',
               existing_type=sa.VARCHAR(length=36),
               nullable=True)

    # Update the enum to include new roles
    op.execute("ALTER TYPE userrole ADD VALUE 'PLATFORM_SUPER_ADMIN'")
    op.execute("ALTER TYPE userrole ADD VALUE 'SCHOOL_OWNER'")
    op.execute("ALTER TYPE userrole ADD VALUE 'SCHOOL_ADMIN'")

    # Update existing roles
    op.execute("UPDATE users SET role = 'SCHOOL_OWNER' WHERE role = 'SUPER_ADMIN'")
    op.execute("UPDATE users SET role = 'SCHOOL_ADMIN' WHERE role = 'ADMIN'")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Revert role changes
    op.execute("UPDATE users SET role = 'SUPER_ADMIN' WHERE role = 'SCHOOL_OWNER'")
    op.execute("UPDATE users SET role = 'ADMIN' WHERE role = 'SCHOOL_ADMIN'")

    op.alter_column('users', 'school_id',
               existing_type=sa.VARCHAR(length=36),
               nullable=False)
    # ### end Alembic commands ###
