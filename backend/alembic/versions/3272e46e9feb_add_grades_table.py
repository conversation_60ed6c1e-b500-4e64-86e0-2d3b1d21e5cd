"""add_grades_table

Revision ID: 3272e46e9feb
Revises: 0f37d9371325
Create Date: 2025-07-11 18:14:33.302245

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '3272e46e9feb'
down_revision = '0f37d9371325'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
