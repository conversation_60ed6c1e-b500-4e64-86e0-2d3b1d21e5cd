"""Add teacher_subjects and class_subjects association tables

Revision ID: 0f37d9371325
Revises: 68bbc83611b7
Create Date: 2025-07-11 16:42:04.749371

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '0f37d9371325'
down_revision = '68bbc83611b7'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('teacher_subjects',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('teacher_id', sa.String(length=36), nullable=False),
    sa.Column('subject_id', sa.String(length=36), nullable=False),
    sa.Column('school_id', sa.String(length=36), nullable=False),
    sa.Column('is_head_of_subject', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.String(length=50), nullable=False),
    sa.Column('updated_at', sa.String(length=50), nullable=False),
    sa.Column('is_deleted', sa.<PERSON>(), nullable=False),
    sa.ForeignKeyConstraint(['school_id'], ['schools.id'], ),
    sa.ForeignKeyConstraint(['subject_id'], ['subjects.id'], ),
    sa.ForeignKeyConstraint(['teacher_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('class_subjects',
    sa.Column('id', sa.String(length=36), nullable=False),
    sa.Column('class_id', sa.String(length=36), nullable=False),
    sa.Column('subject_id', sa.String(length=36), nullable=False),
    sa.Column('school_id', sa.String(length=36), nullable=False),
    sa.Column('is_core', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.String(length=50), nullable=False),
    sa.Column('updated_at', sa.String(length=50), nullable=False),
    sa.Column('is_deleted', sa.Boolean(), nullable=False),
    sa.ForeignKeyConstraint(['class_id'], ['classes.id'], ),
    sa.ForeignKeyConstraint(['school_id'], ['schools.id'], ),
    sa.ForeignKeyConstraint(['subject_id'], ['subjects.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('class_subjects')
    op.drop_table('teacher_subjects')
    # ### end Alembic commands ###
