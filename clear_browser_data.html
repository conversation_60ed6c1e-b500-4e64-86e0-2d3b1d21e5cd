<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clear Browser Data - School Management System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .button {
            background-color: #3b82f6;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background-color: #2563eb;
        }
        .success {
            color: #059669;
            font-weight: bold;
            margin-top: 15px;
        }
        .info {
            background-color: #eff6ff;
            border: 1px solid #bfdbfe;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 Clear Browser Data</h1>
        
        <div class="info">
            <strong>Purpose:</strong> This tool clears corrupted localStorage data that may be causing React errors in the School Management System frontend.
        </div>

        <p>If you're experiencing JSON parsing errors or theme-related issues, click the button below to clear the browser's localStorage:</p>

        <button class="button" onclick="clearLocalStorage()">Clear localStorage</button>
        <button class="button" onclick="clearAllData()">Clear All Browser Data</button>
        
        <div id="result"></div>

        <div class="info">
            <strong>After clearing:</strong>
            <ol>
                <li>Close this tab</li>
                <li>Go back to <a href="http://localhost:3000" target="_blank">http://localhost:3000</a></li>
                <li>Refresh the page (Ctrl+F5 or Cmd+Shift+R)</li>
            </ol>
        </div>
    </div>

    <script>
        function clearLocalStorage() {
            try {
                localStorage.clear();
                document.getElementById('result').innerHTML = 
                    '<div class="success">✅ localStorage cleared successfully!</div>';
            } catch (error) {
                document.getElementById('result').innerHTML = 
                    '<div style="color: red;">❌ Error clearing localStorage: ' + error.message + '</div>';
            }
        }

        function clearAllData() {
            try {
                localStorage.clear();
                sessionStorage.clear();
                
                // Clear cookies for localhost
                document.cookie.split(";").forEach(function(c) { 
                    document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
                });

                document.getElementById('result').innerHTML = 
                    '<div class="success">✅ All browser data cleared successfully!<br>Please refresh the main application.</div>';
            } catch (error) {
                document.getElementById('result').innerHTML = 
                    '<div style="color: red;">❌ Error clearing data: ' + error.message + '</div>';
            }
        }

        // Auto-clear on page load if requested
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('auto') === 'true') {
            clearLocalStorage();
        }
    </script>
</body>
</html>
