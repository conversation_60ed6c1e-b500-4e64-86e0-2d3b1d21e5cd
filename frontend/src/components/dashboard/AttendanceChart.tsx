import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  Legend,
} from 'recharts';
import { useCurrentTerm } from '../../hooks/useCurrentTerm';

const AttendanceChart: React.FC = () => {
  const { currentTerm } = useCurrentTerm();
  const [data, setData] = useState([
    { name: 'Present', value: 85, color: 'rgb(var(--color-primary-500))' },
    { name: 'Absent', value: 10, color: '#ef4444' },
    { name: 'Late', value: 5, color: '#f59e0b' },
  ]);

  useEffect(() => {
    // In a real implementation, you would fetch attendance data based on currentTerm
    // For now, we'll use mock data that could vary by term
    if (currentTerm) {
      // Mock different data for different terms
      const termBasedData = [
        { name: 'Present', value: 85 + (currentTerm.name.includes('First') ? 0 : 3), color: 'rgb(var(--color-primary-500))' },
        { name: 'Absent', value: 10 - (currentTerm.name.includes('First') ? 0 : 2), color: '#ef4444' },
        { name: 'Late', value: 5 - (currentTerm.name.includes('First') ? 0 : 1), color: '#f59e0b' },
      ];
      setData(termBasedData);
    }
  }, [currentTerm]);
  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }: any) => {
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text 
        x={x} 
        y={y} 
        fill="white" 
        textAnchor={x > cx ? 'start' : 'end'} 
        dominantBaseline="central"
        fontSize={12}
        fontWeight="bold"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  return (
    <div className="card p-6">
      <div className="mb-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          Attendance Overview
        </h3>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          {currentTerm
            ? `Student attendance for ${currentTerm.name} (${currentTerm.academic_session})`
            : 'Student attendance distribution this month'
          }
        </p>
      </div>
      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={renderCustomizedLabel}
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
            >
              {data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
            <Tooltip
              contentStyle={{
                backgroundColor: 'var(--tw-color-white)',
                border: '1px solid var(--tw-color-gray-200)',
                borderRadius: '0.5rem',
              }}
            />
            <Legend />
          </PieChart>
        </ResponsiveContainer>
      </div>
      
      {/* Summary stats */}
      <div className="mt-4 grid grid-cols-3 gap-4">
        {data.map((item) => (
          <div key={item.name} className="text-center">
            <div 
              className="w-3 h-3 rounded-full mx-auto mb-1"
              style={{ backgroundColor: item.color }}
            />
            <p className="text-xs text-gray-500 dark:text-gray-400">{item.name}</p>
            <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
              {item.value}%
            </p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default AttendanceChart;
