import React, { useState, useEffect } from 'react';
import {
  BuildingOfficeIcon,
  PlusIcon,
  ClockIcon,
  EyeIcon,
  PencilIcon,
  UserPlusIcon,
  ArrowRightOnRectangleIcon
} from '@heroicons/react/24/outline';
import {
  StarIcon as CrownIcon
} from '@heroicons/react/24/solid';
import { useAuth } from '../../contexts/AuthContext';
import { schoolSelectionService } from '../../services/schoolSelectionService';
import { SchoolOption, OwnedSchoolsResponse } from '../../types';
import LoadingSpinner from '../ui/LoadingSpinner';
import { useToast } from '../../hooks/useToast';
import AddSchoolModal from './AddSchoolModal';
import EditSchoolModal from './EditSchoolModal';

const SchoolManagement: React.FC = () => {
  const { user, selectSchool, updateUser } = useAuth();
  const { showSuccess, showError } = useToast();
  const [schools, setSchools] = useState<SchoolOption[]>([]);
  const [loading, setLoading] = useState(true);
  const [switchingSchool, setSwitchingSchool] = useState<string | null>(null);
  const [showAddOwnerModal, setShowAddOwnerModal] = useState(false);
  const [showAddSchoolModal, setShowAddSchoolModal] = useState(false);
  const [showEditSchoolModal, setShowEditSchoolModal] = useState(false);
  const [selectedSchoolForOwner, setSelectedSchoolForOwner] = useState<string>('');
  const [newOwnerEmail, setNewOwnerEmail] = useState('');
  const [addingOwner, setAddingOwner] = useState(false);

  useEffect(() => {
    loadOwnedSchools();
  }, []);

  const loadOwnedSchools = async () => {
    try {
      setLoading(true);
      const response: OwnedSchoolsResponse = await schoolSelectionService.getOwnedSchools();
      setSchools(response.schools);
    } catch (error) {
      console.error('Failed to load owned schools:', error);
      showError('Failed to load schools');
    } finally {
      setLoading(false);
    }
  };

  const handleSwitchSchool = async (schoolId: string) => {
    try {
      setSwitchingSchool(schoolId);
      await selectSchool(schoolId);
      showSuccess('School switched successfully');
      // Reload the page to refresh the context
      window.location.reload();
    } catch (error) {
      console.error('Failed to switch school:', error);
      showError('Failed to switch school');
    } finally {
      setSwitchingSchool(null);
    }
  };

  const handleAddOwner = async () => {
    if (!newOwnerEmail || !selectedSchoolForOwner) return;

    try {
      setAddingOwner(true);
      await schoolSelectionService.addSchoolOwnership(newOwnerEmail, selectedSchoolForOwner);
      showSuccess('Owner added successfully');
      setShowAddOwnerModal(false);
      setNewOwnerEmail('');
      setSelectedSchoolForOwner('');
    } catch (error: any) {
      console.error('Failed to add owner:', error);
      const message = error.response?.data?.detail || 'Failed to add owner';
      showError(message);
    } finally {
      setAddingOwner(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            School Management
          </h3>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Manage your schools and switch between them.
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowAddOwnerModal(true)}
            className="btn btn-secondary flex items-center space-x-2"
          >
            <UserPlusIcon className="h-5 w-5" />
            <span>Add Owner</span>
          </button>
          <button
            onClick={() => setShowAddSchoolModal(true)}
            className="btn btn-primary flex items-center space-x-2"
          >
            <PlusIcon className="h-5 w-5" />
            <span>Add School</span>
          </button>
        </div>
      </div>

      {/* Schools List */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {schools.map((school) => (
          <div
            key={school.id}
            className="card p-6 hover:shadow-lg transition-shadow duration-200"
          >
            {/* School Header */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3">
                {school.logo_url ? (
                  <img
                    src={school.logo_url}
                    alt={`${school.name} logo`}
                    className="h-12 w-12 rounded-lg object-cover"
                  />
                ) : (
                  <div className="h-12 w-12 rounded-lg bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                    <BuildingOfficeIcon className="h-6 w-6 text-gray-500 dark:text-gray-400" />
                  </div>
                )}
                <div>
                  <div className="flex items-center space-x-2">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                      {school.name}
                    </h4>
                    {school.is_primary && (
                      <CrownIcon className="h-4 w-4 text-yellow-500" title="Primary School" />
                    )}
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {school.code}
                  </p>
                </div>
              </div>
              
              {/* Current School Indicator */}
              {user?.school_id === school.id && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                  Current
                </span>
              )}
            </div>

            {/* Subscription Info */}
            <div className="mb-4">
              <div className="flex items-center justify-between mb-2">
                <span className={`inline-flex items-center px-2 py-1 rounded text-xs font-medium ${
                  school.subscription_status === 'active'
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                    : school.subscription_status === 'trial'
                    ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                    : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                }`}>
                  {school.subscription_plan === 'trial' ? 'Trial' : school.subscription_plan}
                </span>
              </div>
              
              {school.is_trial && school.trial_expires_at && (
                <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                  <ClockIcon className="h-3 w-3 mr-1" />
                  Expires {new Date(school.trial_expires_at).toLocaleDateString()}
                </div>
              )}
            </div>

            {/* Actions */}
            <div className="flex space-x-2">
              {user?.school_id !== school.id && (
                <button
                  onClick={() => handleSwitchSchool(school.id)}
                  disabled={switchingSchool === school.id}
                  className="flex-1 btn btn-primary text-xs flex items-center justify-center space-x-1"
                >
                  {switchingSchool === school.id ? (
                    <LoadingSpinner size="sm" />
                  ) : (
                    <>
                      <ArrowRightOnRectangleIcon className="h-3 w-3" />
                      <span>Switch</span>
                    </>
                  )}
                </button>
              )}
              
              <button className="btn btn-secondary text-xs flex items-center space-x-1">
                <EyeIcon className="h-3 w-3" />
                <span>View</span>
              </button>
              
              {school.is_primary && (
                <button
                  onClick={() => setShowEditSchoolModal(true)}
                  className="btn btn-secondary text-xs flex items-center space-x-1"
                >
                  <PencilIcon className="h-3 w-3" />
                  <span>Edit</span>
                </button>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Add Owner Modal */}
      {showAddOwnerModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Add School Owner
              </h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    School
                  </label>
                  <select
                    value={selectedSchoolForOwner}
                    onChange={(e) => setSelectedSchoolForOwner(e.target.value)}
                    className="input w-full"
                  >
                    <option value="">Select a school</option>
                    {schools.filter(s => s.is_primary).map((school) => (
                      <option key={school.id} value={school.id}>
                        {school.name}
                      </option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Owner Email
                  </label>
                  <input
                    type="email"
                    value={newOwnerEmail}
                    onChange={(e) => setNewOwnerEmail(e.target.value)}
                    placeholder="Enter email address"
                    className="input w-full"
                  />
                </div>
              </div>
              
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => setShowAddOwnerModal(false)}
                  className="btn btn-secondary"
                >
                  Cancel
                </button>
                <button
                  onClick={handleAddOwner}
                  disabled={!newOwnerEmail || !selectedSchoolForOwner || addingOwner}
                  className="btn btn-primary flex items-center space-x-2"
                >
                  {addingOwner ? (
                    <>
                      <LoadingSpinner size="sm" />
                      <span>Adding...</span>
                    </>
                  ) : (
                    <span>Add Owner</span>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Add School Modal */}
      <AddSchoolModal
        isOpen={showAddSchoolModal}
        onClose={() => setShowAddSchoolModal(false)}
        onSuccess={loadOwnedSchools}
      />

      {/* Edit School Modal */}
      <EditSchoolModal
        isOpen={showEditSchoolModal}
        onClose={() => setShowEditSchoolModal(false)}
        onSuccess={() => {
          setShowEditSchoolModal(false);
          loadOwnedSchools();
          // Update user data to refresh logo and school information
          updateUser();
        }}
      />
    </div>
  );
};

export default SchoolManagement;
