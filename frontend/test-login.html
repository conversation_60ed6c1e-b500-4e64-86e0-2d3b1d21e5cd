<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Login</title>
</head>
<body>
    <h1>Test Login</h1>
    <button onclick="testLogin()">Test Login</button>
    <div id="result"></div>

    <script>
        async function testLogin() {
            try {
                const response = await fetch('http://localhost:8000/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'P@$w0rd'
                    })
                });

                const data = await response.json();
                
                if (response.ok) {
                    localStorage.setItem('access_token', data.access_token);
                    localStorage.setItem('user', JSON.stringify(data.user));
                    document.getElementById('result').innerHTML = `
                        <h2>Login Successful!</h2>
                        <p>Token: ${data.access_token.substring(0, 20)}...</p>
                        <p>User: ${data.user.first_name} ${data.user.last_name}</p>
                        <button onclick="testCurrentTerm()">Test Current Term</button>
                    `;
                } else {
                    document.getElementById('result').innerHTML = `
                        <h2>Login Failed</h2>
                        <p>Error: ${data.detail || 'Unknown error'}</p>
                    `;
                }
            } catch (error) {
                document.getElementById('result').innerHTML = `
                    <h2>Network Error</h2>
                    <p>Error: ${error.message}</p>
                `;
            }
        }

        async function testCurrentTerm() {
            try {
                const token = localStorage.getItem('access_token');
                const response = await fetch('http://localhost:8000/api/v1/terms/current', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();
                
                if (response.ok) {
                    document.getElementById('result').innerHTML += `
                        <h3>Current Term Test Successful!</h3>
                        <p>Current Term: ${data.name} (${data.academic_session})</p>
                        <p>Status: ${data.is_current ? 'Current' : 'Not Current'}</p>
                    `;
                } else {
                    document.getElementById('result').innerHTML += `
                        <h3>Current Term Test Failed</h3>
                        <p>Error: ${data.detail || 'Unknown error'}</p>
                    `;
                }
            } catch (error) {
                document.getElementById('result').innerHTML += `
                    <h3>Network Error</h3>
                    <p>Error: ${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
