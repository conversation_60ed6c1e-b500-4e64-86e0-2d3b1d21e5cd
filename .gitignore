# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
uploads/
test.db
*.db

# Frontend (Node.js/React)
node_modules/
**/node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
dist/
build/
.next/
out/
.nuxt/
.vite/
.cache/

# Frontend environment and config
.env.local
.env.development.local
.env.test.local
.env.production.local

# Backend Python specific
*.pyc
__pycache__/
.pytest_cache/
.coverage
htmlcov/
.tox/
.venv/
venv/
env/

# Database and migrations
*.sqlite
*.sqlite3
*.db
alembic/versions/*.py
!alembic/versions/__init__.py

# Logs and temporary files
*.log
*.tmp
*.temp
.DS_Store
Thumbs.db

# Development and testing
.pytest_cache/
coverage.xml
*.cover
.hypothesis/
.coverage.*

# Documentation builds
docs/_build/
site/

# Docker
.dockerignore
docker-compose.override.yml

# Backup files
*.bak
*.backup
*.old

# Media and uploads
uploads/
media/
static/media/

# Configuration files with secrets
.env
.env.production
.env.staging
config/local.py
config/production.py

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.project
.settings/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Package files
*.tar.gz
*.zip
*.rar

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.parcel-cache/

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Additional common patterns
*.log.*
*.tmp.*
.temp/
temp/
tmp/

# Lock files (keep package-lock.json but ignore others)
yarn.lock
pnpm-lock.yaml

# Build outputs
*.tsbuildinfo
.turbo/

# Testing
coverage/
.nyc_output/
test-results/
playwright-report/

# Misc
.DS_Store.*
*.swp.*
*.swo.*
